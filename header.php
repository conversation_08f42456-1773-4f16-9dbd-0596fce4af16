<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo( 'charset' ); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php wp_title( '|', true, 'right' ); ?></title>
    <?php wp_head(); ?>
</head>
<body <?php body_class(); ?>>

<header class="site-header">
    <div class="header-top">
        <div class="container">
            <div class="logo">
                <a href="<?php echo esc_url( home_url( '/' ) ); ?>" rel="home">
                    <img src="<?php echo get_template_directory_uri(); ?>/Logo.png" alt="<?php bloginfo( 'name' ); ?>" class="site-logo">
                </a>
            </div>
            <div class="search-form-container">
                <?php get_search_form(); ?>
            </div>
            <div class="header-right">
                <?php if ( is_user_logged_in() ) : ?>
                    <div class="education-panel">
                        <a href="#" class="education-panel-button" id="education-panel-button">
                            <div class="education-panel-icon">
                                <svg width="15" height="15" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                    <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                </svg>
                            </div>
                            <div class="education-panel-text">
                                <span class="education-panel-main">Egitim Paneli</span>
                                <span class="education-panel-sub">Kurslari Izlemek icin</span>
                            </div>
                        </a>
                    </div>
                <?php endif; ?>
                <div class="user-actions">
                    <?php if ( is_user_logged_in() ) : ?>
                        <?php
                        $current_user = wp_get_current_user();
                        $user_avatar_url = get_avatar_url( $current_user->ID, array( 'size' => 40 ) );
                        ?>
                        <div class="user-profile-dropdown">
                            <button class="user-avatar-button" id="user-avatar-toggle" aria-expanded="false" aria-haspopup="true">
                                <div class="user-avatar-circle">
                                    <img src="<?php echo esc_url( $user_avatar_url ); ?>" alt="<?php echo esc_attr( $current_user->display_name ); ?>" class="user-avatar-img">
                                </div>
                            </button>
                            <div class="user-dropdown-menu" id="user-dropdown-menu" role="menu">
                                <div class="user-info">
                                    <span class="user-name"><?php echo esc_html( $current_user->display_name ); ?></span>
                                    <span class="user-email"><?php echo esc_html( $current_user->user_email ); ?></span>
                                </div>
                                <div class="user-menu-items">
                                    <a href="<?php echo esc_url( get_permalink( get_option('woocommerce_myaccount_page_id') ) ); ?>" class="user-menu-item" role="menuitem">
                                        <svg class="menu-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <circle cx="12" cy="7" r="4" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Hesabim
                                    </a>
                                    <a href="<?php echo esc_url( wp_logout_url( home_url() ) ); ?>" class="user-menu-item logout" role="menuitem">
                                        <svg class="menu-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M9 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <polyline points="16,17 21,12 16,7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                            <line x1="21" y1="12" x2="9" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                        </svg>
                                        Cikis Yap
                                    </a>
                                </div>
                            </div>
                        </div>
                    <?php else : ?>
                        <a href="<?php echo esc_url( get_permalink( get_option('woocommerce_myaccount_page_id') ) ); ?>" class="login-button">
                            <svg class="login-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <polyline points="10,17 15,12 10,7" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <line x1="15" y1="12" x2="3" y2="12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <span class="login-text">
                                <span class="login-main">Giris Yap</span>
                                <span class="login-sub">veya uye ol</span>
                            </span>
                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <div class="header-bottom">
        <div class="container">
            <nav class="main-navigation">
                <?php
                wp_nav_menu( array(
                    'theme_location' => 'primary',
                    'menu_id'        => 'primary-menu',
                    'walker'         => new Dmr_Walker_Nav_Menu(),
                ) );
                ?>
            </nav>
            <div class="cart">
                <button class="cart-button" id="cart-toggle">
                    <svg class="cart-icon" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3 3H5L5.4 5M7 13H17L21 5H5.4M7 13L5.4 5M7 13L4.7 15.3C4.3 15.7 4.6 16.5 5.1 16.5H17M17 13V16.5M9 19.5C9.8 19.5 10.5 20.2 10.5 21S9.8 22.5 9 22.5 7.5 21.8 7.5 21 8.2 19.5 9 19.5ZM20 19.5C20.8 19.5 21.5 20.2 21.5 21S20.8 22.5 20 22.5 18.5 21.8 18.5 21 19.2 19.5 20 19.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                    <?php if ( class_exists( 'WooCommerce' ) && WC()->cart ) : ?>
                        <span class="cart-count"><?php echo WC()->cart->get_cart_contents_count(); ?></span>
                    <?php endif; ?>
                </button>
            </div>
        </div>
    </div>
</header>

<!-- Sepet Sidebar Overlay -->
<div class="cart-sidebar-overlay" id="cart-sidebar-overlay"></div>

<!-- Sepet Sidebar -->
<div id="cart-sidebar" class="cart-sidebar">
    <div class="cart-sidebar-content">
        <div class="cart-sidebar-header">
            <h3>Sepetim</h3>
            <button class="cart-sidebar-close" id="cart-sidebar-close">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M18 6L6 18M6 6L18 18" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                </svg>
            </button>
        </div>
        <div class="cart-sidebar-body">
            <?php if ( class_exists( 'WooCommerce' ) ) : ?>
                <div class="widget_shopping_cart_content">
                    <?php woocommerce_mini_cart(); ?>
                </div>
            <?php else : ?>
                <p>WooCommerce eklentisi aktif degil.</p>
            <?php endif; ?>
        </div>
        <div class="cart-sidebar-footer">
            <div class="cart-footer-info">
                <p><small>Ucretsiz kargo 500 TL ve uzeri alisverislerde</small></p>
            </div>
        </div>
    </div>
</div>
